# TrustAI Environment Configuration

# Database Configuration
DATABASE_URL=sqlite:///trustai.db

# Security Configuration
JWT_SECRET_KEY=your-secret-key-change-in-production
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
SESSION_TIMEOUT=3600

# Fraud Detection Thresholds
HIGH_RISK_THRESHOLD=40
MEDIUM_RISK_THRESHOLD=70
MAX_TRANSACTION_AMOUNT=10000
MAX_DAILY_TRANSACTIONS=20

# Email Configuration (Optional)
SMTP_SERVER=localhost
SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
FROM_EMAIL=<EMAIL>

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379

# Application Configuration
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000
