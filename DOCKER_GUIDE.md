# 🐳 TrustAI Docker Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying TrustAI using Docker containers. We provide multiple deployment options:

- **Development**: Quick setup with SQLite database
- **Production**: Full setup with PostgreSQL, Redis, and Nginx
- **Monitoring**: Optional Prometheus and Grafana integration

## 🚀 Quick Start (Development)

### Prerequisites
- Docker Desktop installed and running
- Docker Compose v2.0+
- 4GB+ available RAM
- 10GB+ available disk space

### 1. Build and Start
```bash
# Windows
docker-build.bat
docker-start.bat

# Linux/Mac
chmod +x docker-build.sh
./docker-build.sh
docker-compose up -d
```

### 2. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/api/health

### 3. Demo Accounts
| Username | Password | Role | Behavior |
|----------|----------|------|----------|
| alice_normal | SecurePass123! | User | Normal patterns |
| bob_suspicious | SecurePass123! | User | Suspicious activity |
| charlie_fraudster | SecurePass123! | User | Fraudulent patterns |
| admin_user | AdminPass123! | Admin | Administrator |

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  React Frontend │    │  Flask Backend  │
│   (Port 80)     │◄──►│   (Port 3000)   │◄──►│   (Port 5000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │ PostgreSQL DB   │◄──►│   Redis Cache   │
                       │   (Port 5432)   │    │   (Port 6379)   │
                       └─────────────────┘    └─────────────────┘
```

## 📦 Docker Images

### Backend Image (trustai-backend)
- **Base**: python:3.11-slim
- **Size**: ~200MB
- **Features**: Flask API, SQLite/PostgreSQL support, JWT auth
- **Health Check**: `/api/health` endpoint

### Frontend Image (trustai-frontend)
- **Base**: nginx:alpine
- **Size**: ~50MB
- **Features**: React SPA, Material-UI, responsive design
- **Health Check**: Root endpoint

## 🔧 Configuration Options

### Environment Variables

#### Backend Configuration
```bash
FLASK_ENV=development|production
JWT_SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///trustai.db
HIGH_RISK_THRESHOLD=40
MEDIUM_RISK_THRESHOLD=70
MAX_TRANSACTION_AMOUNT=10000
```

#### Database Configuration
```bash
POSTGRES_DB=trustai
POSTGRES_USER=trustai
POSTGRES_PASSWORD=secure-password
REDIS_URL=redis://redis:6379
```

### Docker Compose Profiles

#### Default Profile (Development)
```bash
docker-compose up -d
```
- Backend with SQLite
- Frontend
- No external dependencies

#### Production Profile
```bash
docker-compose -f docker-compose.prod.yml up -d
```
- Backend with PostgreSQL
- Frontend with Nginx proxy
- Redis for caching
- Health checks enabled

#### Monitoring Profile
```bash
docker-compose -f docker-compose.prod.yml --profile monitoring up -d
```
- All production services
- Prometheus metrics
- Grafana dashboards

## 🛠️ Development Workflow

### Building Images
```bash
# Build all images
docker-compose build

# Build specific service
docker-compose build backend
docker-compose build frontend

# Force rebuild without cache
docker-compose build --no-cache
```

### Managing Services
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart backend

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Check status
docker-compose ps
```

### Database Operations
```bash
# Initialize database
docker-compose exec backend python init_db.py

# Access database shell
docker-compose exec db psql -U trustai -d trustai

# Backup database
docker-compose exec db pg_dump -U trustai trustai > backup.sql

# Restore database
docker-compose exec -T db psql -U trustai trustai < backup.sql
```

## 🔒 Security Considerations

### Production Security
1. **Change default passwords**:
   ```bash
   export JWT_SECRET_KEY="your-secure-random-key"
   export POSTGRES_PASSWORD="your-secure-db-password"
   ```

2. **Use SSL certificates**:
   ```bash
   # Place certificates in ssl/ directory
   ssl/
   ├── cert.pem
   └── key.pem
   ```

3. **Network isolation**:
   - Services communicate via internal network
   - Only necessary ports exposed
   - Database not accessible externally

### Environment Files
```bash
# Create production environment file
cp .env.docker .env.production

# Edit with secure values
nano .env.production
```

## 📊 Monitoring and Logging

### Application Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Export logs
docker-compose logs backend > backend.log
```

### Health Monitoring
```bash
# Check service health
docker-compose ps

# Manual health checks
curl http://localhost:5000/api/health
curl http://localhost:3000/

# Container resource usage
docker stats
```

### Prometheus Metrics (Optional)
- **URL**: http://localhost:9090
- **Metrics**: Application performance, system resources
- **Alerts**: Custom alerting rules

### Grafana Dashboards (Optional)
- **URL**: http://localhost:3001
- **Login**: admin / admin (change in production)
- **Dashboards**: Pre-configured TrustAI metrics

## 🚨 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check port usage
netstat -tulpn | grep :5000
netstat -tulpn | grep :3000

# Use different ports
docker-compose -p trustai-alt up -d
```

#### Database Connection Issues
```bash
# Check database status
docker-compose exec db pg_isready -U trustai

# Reset database
docker-compose down -v
docker-compose up -d
```

#### Memory Issues
```bash
# Check container memory usage
docker stats

# Increase Docker memory limit
# Docker Desktop > Settings > Resources > Memory
```

#### Build Failures
```bash
# Clean build cache
docker system prune -a

# Rebuild from scratch
docker-compose build --no-cache
```

### Debug Mode
```bash
# Run with debug output
docker-compose up --verbose

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend sh

# Check container logs
docker logs trustai_backend_1
```

## 🔄 Updates and Maintenance

### Updating Images
```bash
# Pull latest base images
docker-compose pull

# Rebuild with updates
docker-compose build --pull

# Update and restart
docker-compose up -d --build
```

### Backup Strategy
```bash
# Backup volumes
docker run --rm -v trustai_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data

# Backup application data
docker-compose exec backend python -c "
from src.database import Database
import json
db = Database()
# Export data logic here
"
```

### Scaling Services
```bash
# Scale backend instances
docker-compose up -d --scale backend=3

# Load balancer configuration needed for multiple instances
```

## 📈 Performance Optimization

### Resource Limits
```yaml
# In docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
```

### Caching Strategy
- **Redis**: Session and API response caching
- **Nginx**: Static file caching
- **Database**: Query result caching

### Monitoring Metrics
- Response time < 500ms
- Memory usage < 80%
- CPU usage < 70%
- Error rate < 1%

## 🎯 Production Deployment Checklist

- [ ] Change all default passwords
- [ ] Configure SSL certificates
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategy
- [ ] Test disaster recovery
- [ ] Set up log aggregation
- [ ] Configure auto-scaling
- [ ] Security audit completed
- [ ] Performance testing done
- [ ] Documentation updated

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Docker logs
3. Check GitHub issues
4. Contact the development team

---

**TrustAI Docker deployment is production-ready! 🚀**
